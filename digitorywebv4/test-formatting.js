// Test script to verify Indian number formatting
function formatIndianNumber(value) {
    // Handle negative numbers
    const isNegative = value < 0;
    const absValue = Math.abs(value);
    
    let formatted;
    
    if (absValue >= 10000000) { // 1 crore and above
        formatted = (absValue / 10000000).toLocaleString('en-IN', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
        }) + ' Cr';
    } else if (absValue >= 100000) { // 1 lakh and above
        formatted = (absValue / 100000).toLocaleString('en-IN', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
        }) + ' L';
    } else if (absValue >= 1000) { // 1 thousand and above
        formatted = (absValue / 1000).toLocaleString('en-IN', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
        }) + ' K';
    } else {
        // For values less than 1000, show exact number with Indian formatting
        formatted = absValue.toLocaleString('en-IN', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
        });
    }
    
    return isNegative ? `-${formatted}` : formatted;
}

function formatExactNumber(value) {
    const numValue = typeof value === 'string' ? parseFloat(value) : value;
    
    if (isNaN(numValue)) {
        return '0';
    }
    
    return numValue.toLocaleString('en-IN', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    });
}

// Test cases
console.log('=== Testing Indian Number Formatting ===');
console.log('');

console.log('Abbreviated Format (for cards):');
console.log('500 =>', formatIndianNumber(500));
console.log('1,500 =>', formatIndianNumber(1500));
console.log('15,000 =>', formatIndianNumber(15000));
console.log('1,50,000 =>', formatIndianNumber(150000));
console.log('15,00,000 =>', formatIndianNumber(1500000));
console.log('1,50,00,000 =>', formatIndianNumber(15000000));
console.log('15,00,00,000 =>', formatIndianNumber(150000000));
console.log('');

console.log('Exact Format (for detailed views):');
console.log('500 =>', formatExactNumber(500));
console.log('1,500 =>', formatExactNumber(1500));
console.log('15,000 =>', formatExactNumber(15000));
console.log('1,50,000 =>', formatExactNumber(150000));
console.log('15,00,000 =>', formatExactNumber(1500000));
console.log('1,50,00,000 =>', formatExactNumber(15000000));
console.log('15,00,00,000 =>', formatExactNumber(150000000));
console.log('');

console.log('Currency Format:');
console.log('₹500 =>', '₹' + formatExactNumber(500));
console.log('₹1,50,000 =>', '₹' + formatIndianNumber(150000));
console.log('₹1,50,00,000 =>', '₹' + formatIndianNumber(15000000));
